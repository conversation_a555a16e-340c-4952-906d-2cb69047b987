{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "E:/Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"content\">\r\n\t\t<image class=\"logo\" src=\"/static/logo.png\"></image>\r\n\t\t<view class=\"text-area\">\r\n\t\t\t<text class=\"title\">{{title}}</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: 'Hello'\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.content {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.logo {\r\n\t\theight: 200rpx;\r\n\t\twidth: 200rpx;\r\n\t\tmargin-top: 200rpx;\r\n\t\tmargin-left: auto;\r\n\t\tmargin-right: auto;\r\n\t\tmargin-bottom: 50rpx;\r\n\t}\r\n\r\n\t.text-area {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.title {\r\n\t\tfont-size: 36rpx;\r\n\t\tcolor: #8f8f94;\r\n\t}\r\n</style>\n", "import MiniProgramPage from 'F:/03_demo_projects/uniapp_demo_1/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": [], "mappings": ";;;AAUC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,OAAO;AAAA,IACR;AAAA,EACA;AAAA,EACD,SAAS;AAAA,EAER;AAAA,EACD,SAAS,CAET;AACD;;;;;;;;ACrBD,GAAG,WAAW,eAAe;"}